export interface Client {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  tax_id?: string;
  created_at: string;
  updated_at: string;
}

export interface InvoiceItem {
  id: string;
  invoice_id: string;
  description: string;
  hsn_sac_code?: string;
  quantity: number;
  rate: number;
  gst_rate: number;
  amount: number;
  created_at: string;
}

export interface Invoice {
  id: string;
  invoice_number: string;
  client_id: string;
  client?: Client;
  invoice_date: string;
  due_date?: string;
  revenue_date?: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  currency: string;
  subtotal: number;
  tax_rate: number;
  tax_amount: number;
  total_amount: number;
  notes?: string;
  terms_conditions?: string;
  payment_instructions?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  items?: InvoiceItem[];
}

export interface InvoiceFormData {
  client_id: string;
  invoice_date: string;
  due_date?: string;
  revenue_date?: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  currency: string;
  tax_rate: number;
  notes?: string;
  terms_conditions?: string;
  payment_instructions?: string;
  items: InvoiceItemFormData[];
}

export interface InvoiceItemFormData {
  description: string;
  hsn_sac_code?: string;
  quantity: number;
  rate: number;
  gst_rate: number;
}

export interface ClientFormData {
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
  tax_id?: string;
}

export interface InvoiceStats {
  total_invoices: number;
  total_amount: number;
  paid_amount: number;
  pending_amount: number;
  overdue_count: number;
  draft_count: number;
}

export interface InvoiceFilters {
  status?: string;
  client_id?: string;
  date_from?: string;
  date_to?: string;
  search?: string;
}

// Company information for invoice template
export interface CompanyInfo {
  name: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  phone: string;
  email: string;
  website: string;
  tax_id: string;
  bank_name: string;
  account_name: string;
  account_number: string;
  swift_code: string;
  branch_name: string;
  bank_address: string;
}

// Default company information based on the template
export const DEFAULT_COMPANY_INFO: CompanyInfo = {
  name: "AproMax Engineering LLP",
  address: "Geetanjali Office 101, Geetanjali Complex",
  city: "Vadodara",
  state: "Gujarat",
  country: "India",
  postal_code: "390020",
  phone: "+91 (265) 6531 8338",
  email: "<EMAIL>",
  website: "www.apromaxengineering.com",
  tax_id: "24AAHFA4847H1ZM",
  bank_name: "HDFC Bank Ltd",
  account_name: "APROMAX ENGINEERING LLP",
  account_number: "**************",
  swift_code: "HDFCINBB",
  branch_name: "Vadodara",
  bank_address: "New Sama Station, Vadodara, Gujarat - 391 005, Ahwa, India"
};

export type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';

export const INVOICE_STATUS_COLORS: Record<InvoiceStatus, string> = {
  draft: 'bg-gray-100 text-gray-800',
  sent: 'bg-blue-100 text-blue-800',
  paid: 'bg-green-100 text-green-800',
  overdue: 'bg-red-100 text-red-800',
  cancelled: 'bg-gray-100 text-gray-600'
};

export const INVOICE_STATUS_LABELS: Record<InvoiceStatus, string> = {
  draft: 'Draft',
  sent: 'Sent',
  paid: 'Paid',
  overdue: 'Overdue',
  cancelled: 'Cancelled'
};

export const CURRENCY_OPTIONS = [
  { value: 'USD', label: 'USD ($)' },
  { value: 'EUR', label: 'EUR (€)' },
  { value: 'GBP', label: 'GBP (£)' },
  { value: 'INR', label: 'INR (₹)' }
];

export const GST_RATES = [
  { value: 0, label: '0%' },
  { value: 5, label: '5%' },
  { value: 12, label: '12%' },
  { value: 18, label: '18%' },
  { value: 28, label: '28%' }
];

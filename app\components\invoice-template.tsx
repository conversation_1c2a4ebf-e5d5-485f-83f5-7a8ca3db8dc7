'use client'

import { forwardRef } from 'react'
import { Invoice, DEFAULT_COMPANY_INFO } from '@/lib/types/invoice'

interface InvoiceTemplateProps {
  invoice: Invoice
  className?: string
}

export const InvoiceTemplate = forwardRef<HTMLDivElement, InvoiceTemplateProps>(
  ({ invoice, className = '' }, ref) => {
    const formatCurrency = (amount: number, currency: string = 'USD') => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
      }).format(amount)
    }

    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    const numberToWords = (amount: number): string => {
      // Simple implementation for demo - in production, use a proper library
      const ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine']
      const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen']
      const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety']

      if (amount === 0) return 'Zero'
      if (amount < 10) return ones[amount]
      if (amount < 20) return teens[amount - 10]
      if (amount < 100) return tens[Math.floor(amount / 10)] + (amount % 10 ? ' ' + ones[amount % 10] : '')
      if (amount < 1000) return ones[Math.floor(amount / 100)] + ' Hundred' + (amount % 100 ? ' ' + numberToWords(amount % 100) : '')

      return `${Math.floor(amount)} Dollars Only` // Simplified for demo
    }

    return (
      <div ref={ref} className={`bg-white p-8 max-w-4xl mx-auto ${className}`}>
        {/* Header Section */}
        <div className="flex justify-between items-start mb-8">
          {/* Company Info */}
          <div className="flex-1">
            <div className="flex items-center mb-4">
              <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                <span className="text-white font-bold text-2xl">A</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-blue-600">{DEFAULT_COMPANY_INFO.name}</h1>
                <p className="text-sm text-gray-600">ENGINEERING LLP</p>
              </div>
            </div>

            <div className="text-sm text-gray-700 space-y-1">
              <p><strong>AproMax Engineering LLP</strong></p>
              <p>Geetanjali Office 101, Geetanjali Complex</p>
              <p>Vadodara, Gujarat 390020, India</p>
              <p>Mobile: +91 (265) 6531 8338</p>
              <p>Email: <EMAIL></p>
              <p>Website: www.apromaxengineering.com</p>
            </div>

            <div className="mt-4 text-sm">
              <p><strong>Branch Office:</strong> 21 Sagar Flat, Vadodara, Gujarat</p>
              <p><strong>Vadodara Office:</strong> Akshar 48-1503, Mota</p>
              <p><strong>GSTIN:</strong> 24**********1ZM</p>
              <p><strong>PAN:</strong> **********</p>
              <p><strong>URL:</strong> +91 (265) 6531 8338</p>
              <p><strong>Email:</strong> <EMAIL></p>
            </div>
          </div>

          {/* Invoice Title and Details */}
          <div className="text-right">
            <h2 className="text-3xl font-bold text-blue-600 mb-6">EXPORT INVOICE</h2>

            <div className="text-sm space-y-2">
              <div className="flex justify-between">
                <span className="font-medium">Invoice No.:</span>
                <span>{invoice.invoice_number}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Revenue Date:</span>
                <span>{invoice.revenue_date ? formatDate(invoice.revenue_date) : formatDate(invoice.invoice_date)}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Invoice Date:</span>
                <span>{formatDate(invoice.invoice_date)}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Contract Number:</span>
                <span>-</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Contract No.:</span>
                <span>-</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bill To Section */}
        <div className="mb-8">
          <div className="bg-blue-600 text-white px-4 py-2 font-bold">
            BILL To
          </div>
          <div className="border border-gray-300 p-4">
            <h3 className="font-bold text-lg mb-2">{invoice.client?.name}</h3>
            <div className="text-sm space-y-1">
              {invoice.client?.address && <p>{invoice.client.address}</p>}
              {invoice.client?.city && invoice.client?.state && (
                <p>{invoice.client.city}, {invoice.client.state}</p>
              )}
              {invoice.client?.country && <p>{invoice.client.country}</p>}
              {invoice.client?.postal_code && <p>{invoice.client.postal_code}</p>}
              {invoice.client?.phone && <p>Phone: {invoice.client.phone}</p>}
              {invoice.client?.email && <p>Email: {invoice.client.email}</p>}
            </div>

            <div className="mt-4 text-sm">
              <p><strong>Subject:</strong> Export Invoice for Project 01 Dashboard Development</p>
            </div>
          </div>
        </div>

        {/* Invoice Items Table */}
        <div className="mb-8">
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-blue-600 text-white">
                <th className="border border-gray-300 px-2 py-2 text-left">Sr. No.</th>
                <th className="border border-gray-300 px-2 py-2 text-left">Task & Description</th>
                <th className="border border-gray-300 px-2 py-2 text-center">HSN/SAC</th>
                <th className="border border-gray-300 px-2 py-2 text-center">Qty</th>
                <th className="border border-gray-300 px-2 py-2 text-right">Rate (in $)</th>
                <th className="border border-gray-300 px-2 py-2 text-center">GST%</th>
                <th className="border border-gray-300 px-2 py-2 text-right">Amount (in $)</th>
              </tr>
            </thead>
            <tbody>
              {invoice.items?.map((item, index) => (
                <tr key={item.id}>
                  <td className="border border-gray-300 px-2 py-2 text-center">{index + 1}</td>
                  <td className="border border-gray-300 px-2 py-2">{item.description}</td>
                  <td className="border border-gray-300 px-2 py-2 text-center">{item.hsn_sac_code || '-'}</td>
                  <td className="border border-gray-300 px-2 py-2 text-center">{item.quantity}</td>
                  <td className="border border-gray-300 px-2 py-2 text-right">{item.rate.toFixed(2)}</td>
                  <td className="border border-gray-300 px-2 py-2 text-center">{item.gst_rate}%</td>
                  <td className="border border-gray-300 px-2 py-2 text-right">{item.amount.toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Totals Section */}
        <div className="flex justify-end mb-8">
          <div className="w-80">
            <div className="border border-gray-300">
              <div className="flex justify-between px-4 py-2 border-b border-gray-300">
                <span>Sub Total</span>
                <span>{formatCurrency(invoice.subtotal, invoice.currency)}</span>
              </div>
              <div className="flex justify-between px-4 py-2 border-b border-gray-300">
                <span>IGST ({invoice.tax_rate}%)</span>
                <span>{formatCurrency(invoice.tax_amount, invoice.currency)}</span>
              </div>
              <div className="flex justify-between px-4 py-2 bg-blue-600 text-white font-bold">
                <span>Total</span>
                <span>{formatCurrency(invoice.total_amount, invoice.currency)}</span>
              </div>
            </div>

            <div className="mt-2 text-sm">
              <p><strong>Total in Words:</strong> {numberToWords(invoice.total_amount)}</p>
            </div>
          </div>
        </div>

        {/* Notes Section */}
        {invoice.notes && (
          <div className="mb-6">
            <h4 className="font-bold mb-2">Notes:</h4>
            <p className="text-sm">{invoice.notes}</p>
          </div>
        )}

        {/* Payment Instructions */}
        <div className="mb-8">
          <h4 className="font-bold mb-2">Payment Instructions:</h4>
          <div className="text-sm space-y-1">
            <p><strong>Payment Method:</strong> Wire transfer</p>
            <p><strong>Payment Terms:</strong> Net 30 days</p>
            <p><strong>Bank Name:</strong> {DEFAULT_COMPANY_INFO.bank_name}</p>
            <p><strong>Account Name:</strong> {DEFAULT_COMPANY_INFO.account_name}</p>
            <p><strong>Account Number:</strong> {DEFAULT_COMPANY_INFO.account_number}</p>
            <p><strong>SWIFT Code:</strong> {DEFAULT_COMPANY_INFO.swift_code}</p>
            <p><strong>Branch Name:</strong> {DEFAULT_COMPANY_INFO.branch_name}</p>
            <p><strong>Bank Address:</strong> {DEFAULT_COMPANY_INFO.bank_address}</p>
            {invoice.payment_instructions && (
              <p className="mt-2">{invoice.payment_instructions}</p>
            )}
          </div>
        </div>

        {/* Terms and Conditions */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          <div>
            <h4 className="font-bold mb-2">Terms & Conditions:</h4>
            <div className="text-xs space-y-2">
              {invoice.terms_conditions ? (
                <p>{invoice.terms_conditions}</p>
              ) : (
                <>
                  <p>1. These terms and conditions supersede and are subject to the Standard Consultant Agreement / Agreement / between the Parties. The services listed in this invoice fall under the agreement. Any additional work beyond the scope will be invoiced separately.</p>
                  <p>2. Currency and Payment: All payments must be made within 30 days from the bank account specified in this invoice. The Client shall bear all bank charges and transaction costs.</p>
                  <p>3. Payment Terms: Unless otherwise specified, full payment is due within 30 days from the invoice date. Late payments will incur interest charges at 1.5% per month on the outstanding amount.</p>
                  <p>4. Force and Clause: Neither party shall be liable for any failure or delay in performance under this Agreement which is due to fire, flood, earthquake, elements of nature or acts of God, acts of war, terrorism, riots, civil disorders, rebellions or revolutions.</p>
                  <p>5. Intellectual Property: All intellectual property and proprietary rights developed in accordance with the agreed terms outlined in the Agreement. Any copyrights or derivative works shall be governed by the terms of the Agreement.</p>
                  <p>6. Refund Policy: Refunds are not available for completed work. Any cancellation will be processed in accordance with the termination clause outlined in the Agreement. Non-refundable fees if any will be clearly stated.</p>
                  <p>7. Governing Law and Jurisdiction: These terms are governed by the laws of India, with legal proceedings subject to the jurisdiction of the courts in Vadodara, Gujarat.</p>
                  <p>8. Dispute Resolution: Any disputes arising from this invoice or the underlying services shall be resolved through arbitration in accordance with the rules of the Indian Arbitration and Conciliation Act, 1996.</p>
                </>
              )}
            </div>
          </div>

          <div>
            <h4 className="font-bold mb-2">Authorized Signatory:</h4>
            <div className="mt-16 border-t border-gray-400 pt-2">
              <p className="text-sm text-center">Authorized Signature</p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-xs text-gray-500 border-t pt-4">
          <p>Thank you for choosing {DEFAULT_COMPANY_INFO.name}. We appreciate your business and are committed to providing you with exceptional service. We look forward to the opportunity to assist you professionally with your business.</p>
        </div>
      </div>
    )
  }
)

InvoiceTemplate.displayName = 'InvoiceTemplate'
{"name": "apromax-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@supabase/supabase-js": "^2.52.0", "@tiptap/core": "^3.0.7", "@tiptap/extension-link": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "appwrite": "^16.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.0.6", "lucide-react": "^0.474.0", "next": "15.4.2", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "slugify": "^1.6.6", "sonner": "^2.0.6", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "eslint": "^9", "eslint-config-next": "15.4.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import BlogEditor from './blog-editor'
import { toast } from "sonner"
import {
  Save,
  Eye,
  Upload,
  Link,
  Info,
  Globe,
  FileText,
  Hash,
} from 'lucide-react'

interface BlogFormData {
  title: string
  slug: string
  excerpt: string
  content: string
  featuredImage: File | null
  publishImmediately: boolean
}

interface ImprovedBlogFormProps {
  onSubmit: (data: BlogFormData) => void
  onCancel: () => void
  initialData?: Partial<BlogFormData>
}

export function ImprovedBlogForm({ onSubmit, onCancel, initialData }: ImprovedBlogFormProps) {
  const [formData, setFormData] = useState<BlogFormData>({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    featuredImage: null,
    publishImmediately: false,
    ...initialData
  })

  const [isSlugManuallyEdited, setIsSlugManuallyEdited] = useState(false)

  // Auto-generate slug from title
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim()
      .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
  }

  // Update slug when title changes (only if not manually edited)
  useEffect(() => {
    if (formData.title && !isSlugManuallyEdited) {
      const newSlug = generateSlug(formData.title)
      setFormData(prev => ({ ...prev, slug: newSlug }))
    }
  }, [formData.title, isSlugManuallyEdited])

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, title: e.target.value }))
  }

  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSlug = e.target.value
    setFormData(prev => ({ ...prev, slug: newSlug }))
    setIsSlugManuallyEdited(true)
  }

  const handleExcerptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, excerpt: e.target.value }))
  }

  const handleContentChange = (content: string) => {
    setFormData(prev => ({ ...prev, content }))
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setFormData(prev => ({ ...prev, featuredImage: file }))
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim()) {
      toast.error('Please enter a title for your blog post')
      return
    }
    
    if (!formData.slug.trim()) {
      toast.error('Please enter a slug for your blog post')
      return
    }
    
    if (!formData.excerpt.trim()) {
      toast.error('Please enter an excerpt for your blog post')
      return
    }
    
    if (!formData.content.trim()) {
      toast.error('Please add some content to your blog post')
      return
    }

    onSubmit(formData)
  }

  const getPreviewUrl = () => {
    if (!formData.slug) return ''
    return `https://yoursite.com/blog/${formData.slug}`
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Create New Blog Post</h2>
          <p className="text-muted-foreground">
            Share your thoughts and insights with your audience
          </p>
        </div>
        <div className="flex gap-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">
            <Save className="w-4 h-4 mr-2" />
            {formData.publishImmediately ? 'Publish' : 'Save Draft'}
          </Button>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Title */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Post Title
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Input
                value={formData.title}
                onChange={handleTitleChange}
                placeholder="Enter your blog post title..."
                className="text-lg font-medium"
                required
              />
            </CardContent>
          </Card>

          {/* Slug */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Hash className="h-5 w-5" />
                URL Slug
              </CardTitle>
              <CardDescription className="flex items-start gap-2">
                <Info className="h-4 w-4 mt-0.5 text-blue-500" />
                <div>
                  The URL-friendly version of your title. This will be used in the blog post URL.
                  {!isSlugManuallyEdited && (
                    <span className="block text-xs text-green-600 mt-1">
                      Auto-generated from title
                    </span>
                  )}
                </div>
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Input
                value={formData.slug}
                onChange={handleSlugChange}
                placeholder="url-friendly-slug"
                required
              />
              {formData.slug && (
                <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                  <Globe className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">Preview URL:</span>
                  <code className="text-sm bg-background px-2 py-1 rounded">
                    {getPreviewUrl()}
                  </code>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Excerpt */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Excerpt
              </CardTitle>
              <CardDescription className="flex items-start gap-2">
                <Info className="h-4 w-4 mt-0.5 text-blue-500" />
                A brief summary or preview of your blog post. This appears in blog listings, search results, and social media previews.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea
                value={formData.excerpt}
                onChange={handleExcerptChange}
                placeholder="Write a compelling summary of your blog post..."
                rows={3}
                className="resize-none"
                required
              />
              <div className="flex justify-between items-center mt-2">
                <span className="text-xs text-muted-foreground">
                  Recommended: 120-160 characters for SEO
                </span>
                <span className="text-xs text-muted-foreground">
                  {formData.excerpt.length} characters
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Content Editor */}
          <Card>
            <CardHeader>
              <CardTitle>Content</CardTitle>
              <CardDescription>
                Write your blog post content using the rich text editor
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BlogEditor
                content={formData.content}
                onChange={handleContentChange}
              />
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Publish Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Publish Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="publishImmediately"
                  checked={formData.publishImmediately}
                  onCheckedChange={(checked) => 
                    setFormData(prev => ({ ...prev, publishImmediately: checked as boolean }))
                  }
                />
                <Label htmlFor="publishImmediately" className="text-sm font-medium">
                  Publish immediately
                </Label>
              </div>
              <p className="text-xs text-muted-foreground">
                {formData.publishImmediately 
                  ? 'Your post will be published and visible to readers immediately.'
                  : 'Your post will be saved as a draft for later publishing.'
                }
              </p>
            </CardContent>
          </Card>

          {/* Featured Image */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Featured Image
              </CardTitle>
              <CardDescription>
                Upload an image to represent your blog post
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />
                {formData.featuredImage && (
                  <div className="flex items-center gap-2 p-2 bg-muted rounded">
                    <Badge variant="secondary">
                      {formData.featuredImage.name}
                    </Badge>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Post Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Post Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Word count</span>
                <span className="text-sm font-medium">
                  {formData.content.replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0).length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Character count</span>
                <span className="text-sm font-medium">
                  {formData.content.replace(/<[^>]*>/g, '').length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Reading time</span>
                <span className="text-sm font-medium">
                  {Math.max(1, Math.ceil(formData.content.replace(/<[^>]*>/g, '').split(/\s+/).length / 200))} min
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </form>
  )
}

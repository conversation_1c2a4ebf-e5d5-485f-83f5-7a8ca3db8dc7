'use client'

import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Eye, 
  Download, 
  Trash2, 
  MoreHorizontal, 
  Search,
  Filter,
  Edit
} from 'lucide-react'
import { Contact, Application, Blog } from '@/lib/supabase'

interface EnhancedTableProps {
  data: Contact[] | Application[] | Blog[]
  type: 'contacts' | 'applications' | 'blogs'
  onView: (item: any) => void
  onEdit?: (item: any) => void
  onDelete: (item: any) => void
  onDownload?: (item: any) => void
}

export function EnhancedTable({ 
  data, 
  type, 
  onView, 
  onEdit, 
  onDelete, 
  onDownload 
}: EnhancedTableProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [sortField, setSortField] = useState<string>('')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  // Filter data based on search term
  const filteredData = data.filter(item => {
    const searchLower = searchTerm.toLowerCase()
    switch (type) {
      case 'contacts':
        const contact = item as Contact
        return (
          contact.first_name.toLowerCase().includes(searchLower) ||
          contact.last_name.toLowerCase().includes(searchLower) ||
          contact.email.toLowerCase().includes(searchLower) ||
          contact.company_name?.toLowerCase().includes(searchLower) ||
          contact.service.toLowerCase().includes(searchLower)
        )
      case 'applications':
        const application = item as Application
        return (
          application.name.toLowerCase().includes(searchLower) ||
          application.email.toLowerCase().includes(searchLower) ||
          application.position.toLowerCase().includes(searchLower)
        )
      case 'blogs':
        const blog = item as Blog
        return (
          blog.title.toLowerCase().includes(searchLower) ||
          blog.excerpt?.toLowerCase().includes(searchLower)
        )
      default:
        return true
    }
  })

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const renderContactRow = (contact: Contact) => (
    <TableRow key={contact.id} className="hover:bg-muted/50 transition-colors">
      <TableCell>
        <div className="font-medium">{contact.first_name} {contact.last_name}</div>
        <div className="text-sm text-muted-foreground">{contact.email}</div>
      </TableCell>
      <TableCell>{contact.company_name || '-'}</TableCell>
      <TableCell>
        <Badge variant="secondary" className="bg-blue-50 text-blue-700 hover:bg-blue-100">
          {contact.service}
        </Badge>
      </TableCell>
      <TableCell className="text-muted-foreground">
        {formatDate(contact.created_at)}
      </TableCell>
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onView(contact)}>
              <Eye className="mr-2 h-4 w-4" />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => onDelete(contact)}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  )

  const renderApplicationRow = (application: Application) => (
    <TableRow key={application.id} className="hover:bg-muted/50 transition-colors">
      <TableCell>
        <div className="font-medium">{application.name}</div>
        <div className="text-sm text-muted-foreground">{application.email}</div>
      </TableCell>
      <TableCell>
        <Badge variant="secondary" className="bg-green-50 text-green-700 hover:bg-green-100">
          {application.position}
        </Badge>
      </TableCell>
      <TableCell>{application.experience} years</TableCell>
      <TableCell className="text-muted-foreground">
        {formatDate(application.created_at)}
      </TableCell>
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onView(application)}>
              <Eye className="mr-2 h-4 w-4" />
              View Details
            </DropdownMenuItem>
            {application.resume_file_id && onDownload && (
              <DropdownMenuItem onClick={() => onDownload(application)}>
                <Download className="mr-2 h-4 w-4" />
                Download Resume
              </DropdownMenuItem>
            )}
            <DropdownMenuItem 
              onClick={() => onDelete(application)}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  )

  const renderBlogRow = (blog: Blog) => (
    <TableRow key={blog.id} className="hover:bg-muted/50 transition-colors">
      <TableCell>
        <div className="font-medium">{blog.title}</div>
        <div className="text-sm text-muted-foreground line-clamp-1">
          {blog.excerpt}
        </div>
      </TableCell>
      <TableCell>
        <Badge 
          variant={blog.published ? "default" : "secondary"}
          className={blog.published 
            ? "bg-green-50 text-green-700 hover:bg-green-100" 
            : "bg-yellow-50 text-yellow-700 hover:bg-yellow-100"
          }
        >
          {blog.published ? 'Published' : 'Draft'}
        </Badge>
      </TableCell>
      <TableCell className="text-muted-foreground">
        {formatDate(blog.created_at)}
      </TableCell>
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {onEdit && (
              <DropdownMenuItem onClick={() => onEdit(blog)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
            )}
            <DropdownMenuItem onClick={() => onView(blog)}>
              <Eye className="mr-2 h-4 w-4" />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => onDelete(blog)}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  )

  const getHeaders = () => {
    switch (type) {
      case 'contacts':
        return ['Contact', 'Company', 'Service', 'Date', 'Actions']
      case 'applications':
        return ['Applicant', 'Position', 'Experience', 'Date', 'Actions']
      case 'blogs':
        return ['Title', 'Status', 'Date', 'Actions']
      default:
        return []
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder={`Search ${type}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-muted-foreground">
            {filteredData.length} {filteredData.length === 1 ? 'item' : 'items'}
          </Badge>
        </div>
      </div>

      <div className="rounded-lg border bg-card">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50">
              {getHeaders().map((header) => (
                <TableHead key={header} className="font-semibold">
                  {header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.length === 0 ? (
              <TableRow>
                <TableCell 
                  colSpan={getHeaders().length} 
                  className="text-center py-8 text-muted-foreground"
                >
                  No {type} found
                </TableCell>
              </TableRow>
            ) : (
              filteredData.map((item) => {
                switch (type) {
                  case 'contacts':
                    return renderContactRow(item as Contact)
                  case 'applications':
                    return renderApplicationRow(item as Application)
                  case 'blogs':
                    return renderBlogRow(item as Blog)
                  default:
                    return null
                }
              })
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}

# Technology Stack

## Framework & Runtime
- **Next.js 15.1.6** - React framework with App Router
- **React 19** - UI library
- **TypeScript 5** - Type safety and development experience
- **Node.js** - Runtime environment

## Backend & Database
- **Appwrite** - Backend-as-a-Service for authentication, database, and file storage
- Collections: contacts, careers, blogs, admin profiles
- File storage buckets for resumes and blog images

## UI & Styling
- **Tailwind CSS 3.4** - Utility-first CSS framework
- **shadcn/ui** - Component library (New York style)
- **Radix UI** - Headless UI primitives
- **Framer Motion** - Animation library
- **Lucide React** - Icon library
- **Class Variance Authority** - Component variant management

## Development Tools
- **ESLint** - Code linting with Next.js config
- **PostCSS** - CSS processing
- **Turbopack** - Fast bundler for development

## Common Commands

```bash
# Development
npm run dev          # Start dev server with Turbopack
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Package Management
npm install          # Install dependencies
npm update           # Update packages
```

## Environment Variables Required
- `NEXT_PUBLIC_APPWRITE_URL`
- `NEXT_PUBLIC_APPWRITE_PROJECT_ID`
- `NEXT_PUBLIC_APPWRITE_DATABASE_ID`
- `NEXT_PUBLIC_APPWRITE_CONTACT_COLLECTION_ID`
- `NEXT_PUBLIC_APPWRITE_CAREERS_COLLECTION_ID`
- `NEXT_PUBLIC_APPWRITE_BLOGS_COLLECTION_ID`
- `NEXT_PUBLIC_APPWRITE_ADMIN_PROFILES_COLLECTION_ID`
- `NEXT_PUBLIC_APPWRITE_CAREERS_BUCKET_ID`
- `NEXT_PUBLIC_APPWRITE_BLOG_IMAGES_BUCKET_ID`
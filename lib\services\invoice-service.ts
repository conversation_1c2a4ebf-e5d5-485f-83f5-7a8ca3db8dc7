import { createClient } from '@/lib/supabase/client';
import { 
  Invoice, 
  InvoiceFormData, 
  InvoiceItem, 
  Client, 
  ClientFormData,
  InvoiceStats,
  InvoiceFilters 
} from '@/lib/types/invoice';

const supabase = createClient();

export class InvoiceService {
  // Client operations
  static async getClients(): Promise<Client[]> {
    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .order('name');

    if (error) throw error;
    return data || [];
  }

  static async getClient(id: string): Promise<Client | null> {
    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  }

  static async createClient(clientData: ClientFormData): Promise<Client> {
    const { data, error } = await supabase
      .from('clients')
      .insert([clientData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateClient(id: string, clientData: Partial<ClientFormData>): Promise<Client> {
    const { data, error } = await supabase
      .from('clients')
      .update({ ...clientData, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteClient(id: string): Promise<void> {
    const { error } = await supabase
      .from('clients')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Invoice operations
  static async getInvoices(filters?: InvoiceFilters): Promise<Invoice[]> {
    let query = supabase
      .from('invoices')
      .select(`
        *,
        client:clients(*),
        items:invoice_items(*)
      `)
      .order('created_at', { ascending: false });

    if (filters?.status) {
      query = query.eq('status', filters.status);
    }

    if (filters?.client_id) {
      query = query.eq('client_id', filters.client_id);
    }

    if (filters?.date_from) {
      query = query.gte('invoice_date', filters.date_from);
    }

    if (filters?.date_to) {
      query = query.lte('invoice_date', filters.date_to);
    }

    if (filters?.search) {
      query = query.or(`invoice_number.ilike.%${filters.search}%,client.name.ilike.%${filters.search}%`);
    }

    const { data, error } = await query;

    if (error) throw error;
    return data || [];
  }

  static async getInvoice(id: string): Promise<Invoice | null> {
    const { data, error } = await supabase
      .from('invoices')
      .select(`
        *,
        client:clients(*),
        items:invoice_items(*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  }

  static async generateInvoiceNumber(): Promise<string> {
    const { data, error } = await supabase
      .rpc('generate_invoice_number');

    if (error) throw error;
    return data;
  }

  static async createInvoice(invoiceData: InvoiceFormData): Promise<Invoice> {
    const invoiceNumber = await this.generateInvoiceNumber();
    
    // Calculate totals
    const subtotal = invoiceData.items.reduce((sum, item) => 
      sum + (item.quantity * item.rate), 0
    );
    
    const taxAmount = (subtotal * invoiceData.tax_rate) / 100;
    const totalAmount = subtotal + taxAmount;

    // Create invoice
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .insert([{
        invoice_number: invoiceNumber,
        client_id: invoiceData.client_id,
        invoice_date: invoiceData.invoice_date,
        due_date: invoiceData.due_date,
        revenue_date: invoiceData.revenue_date,
        status: invoiceData.status,
        currency: invoiceData.currency,
        subtotal,
        tax_rate: invoiceData.tax_rate,
        tax_amount: taxAmount,
        total_amount: totalAmount,
        notes: invoiceData.notes,
        terms_conditions: invoiceData.terms_conditions,
        payment_instructions: invoiceData.payment_instructions,
        created_by: (await supabase.auth.getUser()).data.user?.id
      }])
      .select()
      .single();

    if (invoiceError) throw invoiceError;

    // Create invoice items
    const itemsWithInvoiceId = invoiceData.items.map(item => ({
      invoice_id: invoice.id,
      description: item.description,
      hsn_sac_code: item.hsn_sac_code,
      quantity: item.quantity,
      rate: item.rate,
      gst_rate: item.gst_rate,
      amount: item.quantity * item.rate
    }));

    const { error: itemsError } = await supabase
      .from('invoice_items')
      .insert(itemsWithInvoiceId);

    if (itemsError) throw itemsError;

    return this.getInvoice(invoice.id) as Promise<Invoice>;
  }

  static async updateInvoice(id: string, invoiceData: Partial<InvoiceFormData>): Promise<Invoice> {
    // Calculate totals if items are provided
    let updateData: any = { ...invoiceData, updated_at: new Date().toISOString() };
    
    if (invoiceData.items) {
      const subtotal = invoiceData.items.reduce((sum, item) => 
        sum + (item.quantity * item.rate), 0
      );
      
      const taxAmount = (subtotal * (invoiceData.tax_rate || 0)) / 100;
      const totalAmount = subtotal + taxAmount;

      updateData = {
        ...updateData,
        subtotal,
        tax_amount: taxAmount,
        total_amount: totalAmount
      };

      // Update invoice items
      await supabase
        .from('invoice_items')
        .delete()
        .eq('invoice_id', id);

      const itemsWithInvoiceId = invoiceData.items.map(item => ({
        invoice_id: id,
        description: item.description,
        hsn_sac_code: item.hsn_sac_code,
        quantity: item.quantity,
        rate: item.rate,
        gst_rate: item.gst_rate,
        amount: item.quantity * item.rate
      }));

      const { error: itemsError } = await supabase
        .from('invoice_items')
        .insert(itemsWithInvoiceId);

      if (itemsError) throw itemsError;
    }

    const { data, error } = await supabase
      .from('invoices')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return this.getInvoice(id) as Promise<Invoice>;
  }

  static async deleteInvoice(id: string): Promise<void> {
    const { error } = await supabase
      .from('invoices')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  static async updateInvoiceStatus(id: string, status: string): Promise<Invoice> {
    const { data, error } = await supabase
      .from('invoices')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return this.getInvoice(id) as Promise<Invoice>;
  }

  static async getInvoiceStats(): Promise<InvoiceStats> {
    const { data: invoices, error } = await supabase
      .from('invoices')
      .select('status, total_amount');

    if (error) throw error;

    const stats = {
      total_invoices: invoices.length,
      total_amount: invoices.reduce((sum, inv) => sum + inv.total_amount, 0),
      paid_amount: invoices
        .filter(inv => inv.status === 'paid')
        .reduce((sum, inv) => sum + inv.total_amount, 0),
      pending_amount: invoices
        .filter(inv => ['sent', 'overdue'].includes(inv.status))
        .reduce((sum, inv) => sum + inv.total_amount, 0),
      overdue_count: invoices.filter(inv => inv.status === 'overdue').length,
      draft_count: invoices.filter(inv => inv.status === 'draft').length
    };

    return stats;
  }
}

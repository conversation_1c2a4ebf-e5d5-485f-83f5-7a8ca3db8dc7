# Apromax Admin Panel

A modern, secure admin panel built with Next.js, Supabase, and shadcn/ui for managing contacts, job applications, and blog content.

## 🚀 Features

- **Modern UI**: Beautiful, responsive interface built with shadcn/ui components
- **Secure Authentication**: Supabase Auth with JWT validation
- **Dashboard**: Comprehensive overview with statistics and recent activity
- **Contact Management**: View and manage customer inquiries
- **Job Applications**: Handle job applications with resume downloads
- **Blog Management**: Create, edit, and publish blog posts with rich text editor
- **File Storage**: Secure file uploads for resumes and blog images
- **Row Level Security**: Database-level security policies
- **Real-time Updates**: Live data synchronization

## 🛠️ Tech Stack

- **Framework**: Next.js 15.4.2 with App Router
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **UI Components**: shadcn/ui + Radix UI
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Rich Text Editor**: TipTap
- **Animations**: Framer Motion

## 📋 Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd apromax-admin
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   Create a `.env.local` file in the root directory:
   ```env
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   NEXT_PUBLIC_SUPABASE_PROJECT_ID=your_project_id
   ```

## 🗄️ Database Setup

The application uses the following database schema:

### Tables
- `contacts` - Customer contact inquiries
- `applications` - Job applications
- `blogs` - Blog posts and content
- `admin_profiles` - Admin user profiles

### Storage Buckets
- `resumes` - Resume file storage (private)
- `blog-images` - Blog image storage (public)

### Row Level Security
All tables have RLS enabled with policies for authenticated users.

## 🚀 Getting Started

1. **Run the development server**
   ```bash
   npm run dev
   ```

2. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

3. **Create an admin user**
   Use Supabase Auth to create your first admin user, or use the Supabase dashboard.

## 📱 Usage

### Dashboard
- View statistics and recent activity
- Quick access to create new content
- Overview of all data

### Contact Management
- View all customer inquiries
- Search and filter contacts
- View detailed contact information
- Delete unwanted contacts

### Job Applications
- Review job applications
- Download resumes
- Filter by position or experience
- Manage application lifecycle

### Blog Management
- Create new blog posts with rich text editor
- Upload featured images
- Publish or save as drafts
- Edit existing posts
- SEO-friendly slug generation

## 🔒 Security Features

- **Authentication**: Secure login with Supabase Auth
- **Row Level Security**: Database-level access control
- **JWT Validation**: Secure session management
- **File Upload Security**: Controlled file access
- **Environment Variables**: Secure configuration management

## 🎨 UI Components

Built with modern, accessible components:
- Responsive design for all screen sizes
- Dark/light mode support
- Loading states and error handling
- Toast notifications
- Modal dialogs
- Enhanced tables with search and filtering

## 📁 Project Structure

```
apromax-admin/
├── app/
│   ├── components/          # React components
│   ├── globals.css         # Global styles
│   ├── layout.tsx          # Root layout
│   └── page.tsx            # Main admin panel
├── components/ui/          # shadcn/ui components
├── hooks/                  # Custom React hooks
├── lib/                    # Utility functions and services
│   ├── auth.ts            # Authentication service
│   ├── database.ts        # Database operations
│   ├── storage.ts         # File storage service
│   └── supabase.ts        # Supabase client
└── public/                # Static assets
```

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms
The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation
- Open an issue on GitHub
- Contact the development team

---

Built with ❤️ using Next.js and Supabase

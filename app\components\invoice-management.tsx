'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { InvoiceList } from './invoice-list'
import { InvoiceForm } from './invoice-form'
import { InvoiceTemplate } from './invoice-template'
import { Invoice } from '@/lib/types/invoice'
import { Printer, Download, X } from 'lucide-react'

type ViewMode = 'list' | 'create' | 'edit' | 'view'
sonner
export function InvoiceManagement() {
  const [viewMode, setViewMode] = useState<ViewMode>('list')
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null)
  const [showPreview, setShowPreview] = useState(false)

  const handleCreateInvoice = () => {
    setSelectedInvoice(null)
    setViewMode('create')
  }

  const handleEditInvoice = (invoice: Invoice) => {
    setSelectedInvoice(invoice)
    setViewMode('edit')
  }

  const handleViewInvoice = (invoice: Invoice) => {
    setSelectedInvoice(invoice)
    setShowPreview(true)
  }

  const handleSaveInvoice = (invoice: Invoice) => {
    setViewMode('list')
    setSelectedInvoice(null)
  }

  const handleCancel = () => {
    setViewMode('list')
    setSelectedInvoice(null)
  }

  const handlePrint = () => {
    window.print()
  }

  const handleDownloadPDF = () => {
    // TODO: Implement PDF generation
    console.log('Download PDF functionality to be implemented')
  }

  if (viewMode === 'create' || viewMode === 'edit') {
    return (
      <InvoiceForm
        invoice={selectedInvoice || undefined}
        onSave={handleSaveInvoice}
        onCancel={handleCancel}
      />
    )
  }

  return (
    <>
      <InvoiceList
        onCreateInvoice={handleCreateInvoice}
        onEditInvoice={handleEditInvoice}
        onViewInvoice={handleViewInvoice}
      />

      {/* Invoice Preview Modal */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <DialogTitle>
                Invoice Preview - {selectedInvoice?.invoice_number}
              </DialogTitle>
              <div className="flex items-center gap-2">
                <Button onClick={handlePrint} variant="outline" size="sm">
                  <Printer className="w-4 h-4 mr-2" />
                  Print
                </Button>
                <Button onClick={handleDownloadPDF} variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Download PDF
                </Button>
                <Button 
                  onClick={() => setShowPreview(false)} 
                  variant="ghost" 
                  size="sm"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </DialogHeader>
          
          {selectedInvoice && (
            <div className="mt-4">
              <InvoiceTemplate 
                invoice={selectedInvoice} 
                className="print:shadow-none"
              />
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Print Styles */}
      <style jsx global>{`
        @media print {
          body * {
            visibility: hidden;
          }
          .print\\:shadow-none,
          .print\\:shadow-none * {
            visibility: visible;
          }
          .print\\:shadow-none {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
          
          /* Hide dialog overlay and other elements during print */
          [role="dialog"],
          .fixed,
          .sticky {
            display: none !important;
          }
          
          /* Show only the invoice template during print */
          .print\\:shadow-none {
            display: block !important;
            position: static !important;
            box-shadow: none !important;
            margin: 0 !important;
            padding: 20px !important;
          }
        }
      `}</style>
    </>
  )
}

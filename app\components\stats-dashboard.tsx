'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  Briefcase, 
  FileText, 
  TrendingUp,
  Mail,
  Calendar,
  Eye
} from 'lucide-react'
import { Contact, Application, Blog } from '@/lib/supabase'

interface StatsDashboardProps {
  contacts: Contact[]
  applications: Application[]
  blogs: Blog[]
}

export function StatsDashboard({ contacts, applications, blogs }: StatsDashboardProps) {
  const publishedBlogs = blogs.filter(blog => blog.published)
  const draftBlogs = blogs.filter(blog => !blog.published)
  
  const recentContacts = contacts.filter(contact => {
    const contactDate = new Date(contact.created_at)
    const weekAgo = new Date()
    weekAgo.setDate(weekAgo.getDate() - 7)
    return contactDate > weekAgo
  }).length

  const recentApplications = applications.filter(application => {
    const appDate = new Date(application.created_at)
    const weekAgo = new Date()
    weekAgo.setDate(weekAgo.getDate() - 7)
    return appDate > weekAgo
  }).length

  const stats = [
    {
      title: 'Total Contacts',
      value: contacts.length,
      description: `+${recentContacts} this week`,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Job Applications',
      value: applications.length,
      description: `+${recentApplications} this week`,
      icon: Briefcase,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Published Blogs',
      value: publishedBlogs.length,
      description: `${draftBlogs.length} drafts`,
      icon: FileText,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Total Content',
      value: blogs.length,
      description: 'All blog posts',
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
      {stats.map((stat, index) => (
        <Card key={index} className="relative overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-lg ${stat.bgColor}`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground mt-1">
              {stat.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

interface RecentActivityProps {
  contacts: Contact[]
  applications: Application[]
  blogs: Blog[]
}

export function RecentActivity({ contacts, applications, blogs }: RecentActivityProps) {
  // Get recent items from all collections
  const recentItems = [
    ...contacts.slice(0, 3).map(item => ({
      type: 'contact',
      title: `${item.first_name} ${item.last_name}`,
      subtitle: item.company_name || item.email,
      time: new Date(item.created_at),
      icon: Mail,
      color: 'text-blue-600'
    })),
    ...applications.slice(0, 3).map(item => ({
      type: 'application',
      title: item.name,
      subtitle: item.position,
      time: new Date(item.created_at),
      icon: Briefcase,
      color: 'text-green-600'
    })),
    ...blogs.slice(0, 2).map(item => ({
      type: 'blog',
      title: item.title,
      subtitle: item.published ? 'Published' : 'Draft',
      time: new Date(item.updated_at || item.created_at),
      icon: FileText,
      color: 'text-purple-600'
    }))
  ].sort((a, b) => b.time.getTime() - a.time.getTime()).slice(0, 5)

  const formatTimeAgo = (date: Date) => {
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    return date.toLocaleDateString()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Recent Activity
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {recentItems.length === 0 ? (
          <p className="text-muted-foreground text-center py-4">No recent activity</p>
        ) : (
          recentItems.map((item, index) => (
            <div key={index} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
              <div className={`p-2 rounded-lg bg-muted`}>
                <item.icon className={`h-4 w-4 ${item.color}`} />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{item.title}</p>
                <p className="text-xs text-muted-foreground truncate">{item.subtitle}</p>
              </div>
              <div className="text-xs text-muted-foreground">
                {formatTimeAgo(item.time)}
              </div>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  )
}

'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Loader2 } from 'lucide-react'
import { ClientOnly } from './client-only'

interface ProtectedRouteProps {
  children: React.ReactNode
}

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const router = useRouter()
  const { user, loading } = useAuth()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin')
    }
  }, [user, loading, router])

  const LoadingScreen = () => (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950">
      <div className="text-center space-y-4">
        <Loader2 className="w-12 h-12 animate-spin text-blue-600 mx-auto" />
        <p className="text-lg text-muted-foreground">
          Loading admin panel...
        </p>
      </div>
    </div>
  )

  return (
    <ClientOnly fallback={<LoadingScreen />}>
      {loading ? (
        <LoadingScreen />
      ) : user ? (
        children
      ) : null}
    </ClientOnly>
  )
}

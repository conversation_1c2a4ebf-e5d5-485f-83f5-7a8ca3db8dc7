'use client'

import { useState } from 'react'
import { Loader2, Upload, X } from 'lucide-react'
import Image from 'next/image'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { StorageService } from '@/lib/storage'

interface ImageUploadProps {
  onUpload: (file: File) => Promise<void>
  currentImageId?: string
  bucketId: string
  onRemove?: () => void
}

export function ImageUpload({ onUpload, currentImageId, bucketId, onRemove }: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setIsUploading(true)
    try {
      await onUpload(file)
    } finally {
      setIsUploading(false)
    }
  }

  const getImageUrl = (imageId: string) => {
    if (bucketId === 'blog-images') {
      return StorageService.getBlogImageUrl(imageId)
    }
    return StorageService.getPublicUrl(bucketId, imageId)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <Button
          type="button"
          variant="outline"
          disabled={isUploading}
          onClick={() => document.getElementById('imageUpload')?.click()}
          className={cn(
            "relative",
            isUploading && "opacity-50 cursor-not-allowed"
          )}
        >
          {isUploading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <Upload className="w-4 h-4 mr-2" />
              Upload Image
            </>
          )}
        </Button>
        <input
          id="imageUpload"
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="hidden"
        />
      </div>

      {currentImageId && (
        <div className="relative w-full aspect-video rounded-lg overflow-hidden border group">
          <Image
            src={getImageUrl(currentImageId)}
            alt="Uploaded image"
            fill
            className="object-cover"
          />
          {onRemove && (
            <Button
              type="button"
              variant="destructive"
              size="sm"
              onClick={onRemove}
              className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      )}
    </div>
  )
}
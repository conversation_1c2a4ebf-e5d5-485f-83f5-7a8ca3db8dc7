import { supabase } from './supabase'
import { BUCKETS } from './supabase'

export class StorageService {
  // Upload file to storage
  static async uploadFile(bucket: string, path: string, file: File) {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: '3600',
        upsert: false
      })

    if (error) {
      throw new Error(`Error uploading file: ${error.message}`)
    }

    return data
  }

  // Get public URL for a file
  static getPublicUrl(bucket: string, path: string) {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(path)

    return data.publicUrl
  }

  // Get signed URL for private files
  static async getSignedUrl(bucket: string, path: string, expiresIn = 3600) {
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(path, expiresIn)

    if (error) {
      throw new Error(`Error creating signed URL: ${error.message}`)
    }

    return data.signedUrl
  }

  // Delete file from storage
  static async deleteFile(bucket: string, path: string) {
    const { error } = await supabase.storage
      .from(bucket)
      .remove([path])

    if (error) {
      throw new Error(`Error deleting file: ${error.message}`)
    }
  }

  // List files in a bucket
  static async listFiles(bucket: string, path = '') {
    const { data, error } = await supabase.storage
      .from(bucket)
      .list(path)

    if (error) {
      throw new Error(`Error listing files: ${error.message}`)
    }

    return data
  }

  // Resume-specific operations
  static async uploadResume(file: File, fileName?: string) {
    const fileExt = file.name.split('.').pop()
    const filePath = `${Date.now()}-${fileName || 'resume'}.${fileExt}`
    
    return this.uploadFile(BUCKETS.RESUMES, filePath, file)
  }

  static async getResumeUrl(path: string) {
    return this.getSignedUrl(BUCKETS.RESUMES, path)
  }

  static async deleteResume(path: string) {
    return this.deleteFile(BUCKETS.RESUMES, path)
  }

  // Blog image-specific operations
  static async uploadBlogImage(file: File, fileName?: string) {
    const fileExt = file.name.split('.').pop()
    const filePath = `${Date.now()}-${fileName || 'blog-image'}.${fileExt}`
    
    const data = await this.uploadFile(BUCKETS.BLOG_IMAGES, filePath, file)
    
    // Return both the upload data and the public URL
    return {
      ...data,
      publicUrl: this.getPublicUrl(BUCKETS.BLOG_IMAGES, data.path)
    }
  }

  static getBlogImageUrl(path: string) {
    return this.getPublicUrl(BUCKETS.BLOG_IMAGES, path)
  }

  static async deleteBlogImage(path: string) {
    return this.deleteFile(BUCKETS.BLOG_IMAGES, path)
  }

  // Utility function to generate unique file names
  static generateFileName(originalName: string, prefix = '') {
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const fileExt = originalName.split('.').pop()
    
    return `${prefix}${timestamp}-${randomString}.${fileExt}`
  }
}

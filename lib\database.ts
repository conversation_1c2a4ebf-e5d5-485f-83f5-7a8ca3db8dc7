import { supabase } from './supabase'
import { Contact, Application, Blog, AdminProfile, TABLES } from './supabase'

export class DatabaseService {
  // Contacts operations
  static async getContacts() {
    const { data, error } = await supabase
      .from(TABLES.CONTACTS)
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Error fetching contacts: ${error.message}`)
    }

    return data as Contact[]
  }

  static async createContact(contact: Omit<Contact, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from(TABLES.CONTACTS)
      .insert(contact)
      .select()
      .single()

    if (error) {
      throw new Error(`Error creating contact: ${error.message}`)
    }

    return data as Contact
  }

  static async deleteContact(id: string) {
    const { error } = await supabase
      .from(TABLES.CONTACTS)
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Error deleting contact: ${error.message}`)
    }
  }

  // Applications operations
  static async getApplications() {
    const { data, error } = await supabase
      .from(TABLES.APPLICATIONS)
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Error fetching applications: ${error.message}`)
    }

    return data as Application[]
  }

  static async createApplication(application: Omit<Application, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from(TABLES.APPLICATIONS)
      .insert(application)
      .select()
      .single()

    if (error) {
      throw new Error(`Error creating application: ${error.message}`)
    }

    return data as Application
  }

  static async deleteApplication(id: string) {
    const { error } = await supabase
      .from(TABLES.APPLICATIONS)
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Error deleting application: ${error.message}`)
    }
  }

  // Blogs operations
  static async getBlogs() {
    const { data, error } = await supabase
      .from(TABLES.BLOGS)
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Error fetching blogs: ${error.message}`)
    }

    return data as Blog[]
  }

  static async getBlogBySlug(slug: string) {
    const { data, error } = await supabase
      .from(TABLES.BLOGS)
      .select('*')
      .eq('slug', slug)
      .single()

    if (error) {
      throw new Error(`Error fetching blog: ${error.message}`)
    }

    return data as Blog
  }

  static async createBlog(blog: Omit<Blog, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from(TABLES.BLOGS)
      .insert(blog)
      .select()
      .single()

    if (error) {
      throw new Error(`Error creating blog: ${error.message}`)
    }

    return data as Blog
  }

  static async updateBlog(id: string, blog: Partial<Omit<Blog, 'id' | 'created_at'>>) {
    const { data, error } = await supabase
      .from(TABLES.BLOGS)
      .update({ ...blog, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Error updating blog: ${error.message}`)
    }

    return data as Blog
  }

  static async deleteBlog(id: string) {
    const { error } = await supabase
      .from(TABLES.BLOGS)
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Error deleting blog: ${error.message}`)
    }
  }

  // Admin profiles operations
  static async getAdminProfile(userId: string) {
    const { data, error } = await supabase
      .from(TABLES.ADMIN_PROFILES)
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
      throw new Error(`Error fetching admin profile: ${error.message}`)
    }

    return data as AdminProfile | null
  }

  static async createAdminProfile(profile: Omit<AdminProfile, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from(TABLES.ADMIN_PROFILES)
      .insert(profile)
      .select()
      .single()

    if (error) {
      throw new Error(`Error creating admin profile: ${error.message}`)
    }

    return data as AdminProfile
  }

  static async updateAdminProfile(userId: string, profile: Partial<Omit<AdminProfile, 'id' | 'user_id' | 'created_at'>>) {
    const { data, error } = await supabase
      .from(TABLES.ADMIN_PROFILES)
      .update({ ...profile, updated_at: new Date().toISOString() })
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      throw new Error(`Error updating admin profile: ${error.message}`)
    }

    return data as AdminProfile
  }
}

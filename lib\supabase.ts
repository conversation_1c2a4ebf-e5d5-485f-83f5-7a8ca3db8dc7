import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Types for our database tables
export interface Contact {
  id: string
  first_name: string
  last_name: string
  email: string
  company_name: string
  service: string
  message?: string
  created_at: string
}

export interface Application {
  id: string
  name: string
  email: string
  position: string
  experience: number
  resume_file_id?: string
  resume_file_url?: string
  phone?: string
  cover_letter?: string
  created_at: string
}

export interface Blog {
  id: string
  title: string
  content: string
  excerpt: string
  published: boolean
  slug: string
  author_id: string
  featured_image?: string
  featured_image_url?: string
  created_at: string
  updated_at: string
}

export interface AdminProfile {
  id: string
  user_id: string
  linkedin_url?: string
  created_at: string
  updated_at: string
}

// Database table names
export const TABLES = {
  CONTACTS: 'contacts',
  APPLICATIONS: 'applications', 
  BLOGS: 'blogs',
  ADMIN_PROFILES: 'admin_profiles'
} as const

// Storage bucket names
export const BUCKETS = {
  RESUMES: 'resumes',
  BLOG_IMAGES: 'blog-images'
} as const

"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { SidebarProvider, SidebarInset, SidebarTrigger } from "@/components/ui/sidebar";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { AppSidebar } from "./components/app-sidebar";
import { ProfilePage } from "./components/profile-page";
import { ImprovedBlogForm } from "./components/improved-blog-form";
import { InvoiceManagement } from "./components/invoice-management";
import { StatsDashboard, RecentActivity } from "./components/stats-dashboard";
import { EnhancedTable } from "./components/enhanced-table";
import { ProtectedRoute } from "./components/protected-route";
import { useAuth } from "@/hooks/useAuth";
import { DatabaseService } from "@/lib/database";
import { StorageService } from "@/lib/storage";
import { Contact, Application, Blog } from "@/lib/supabase";
import { toast } from "sonner";
import {
  RefreshCw,
  FileText,
  Plus,
} from "lucide-react";

function AdminPanelContent() {
  const router = useRouter();
  const { user, signOut } = useAuth();
  const [activeTab, setActiveTab] = useState("dashboard");
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [applications, setApplications] = useState<Application[]>([]);
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [linkedinUrl, setLinkedinUrl] = useState("");
  const [showBlogForm, setShowBlogForm] = useState(false);

  // Fetch data when user is authenticated
  useEffect(() => {
    if (user) {
      fetchData();
    }
  }, [user]);

  const fetchData = async () => {
    try {
      setIsRefreshing(true);
      const [contactsData, applicationsData, blogsData] = await Promise.all([
        DatabaseService.getContacts(),
        DatabaseService.getApplications(),
        DatabaseService.getBlogs(),
      ]);
      
      setContacts(contactsData);
      setApplications(applicationsData);
      setBlogs(blogsData);
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Failed to fetch data");
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleLogout = async () => {
    try {
      await signOut();
      router.push("/auth/signin");
    } catch (error) {
      console.error("Error signing out:", error);
      toast.error("Failed to sign out");
    }
  };

  const handleRefresh = () => {
    fetchData();
  };

  const handleBlogSubmit = async (formData: any) => {
    try {
      // Handle image upload if present
      let featuredImageUrl = "";
      if (formData.featuredImage) {
        featuredImageUrl = await StorageService.uploadImage(formData.featuredImage);
      }

      const blogData = {
        title: formData.title,
        content: formData.content,
        excerpt: formData.excerpt,
        slug: formData.slug,
        published: formData.publishImmediately,
        author_id: user?.id || "",
        featured_image: featuredImageUrl,
      };

      await DatabaseService.createBlog(blogData);
      toast.success("Blog post created successfully!");
      setShowBlogForm(false);
      fetchData(); // Refresh data
    } catch (error) {
      console.error("Error creating blog:", error);
      toast.error("Failed to create blog post");
    }
  };

  const getPageTitle = () => {
    switch (activeTab) {
      case "dashboard":
        return "Dashboard";
      case "contacts":
        return "Contacts";
      case "jobs":
        return "Job Applications";
      case "invoices":
        return "Invoice Management";
      case "blogs":
        return "Blog Management";
      case "profile":
        return "Profile";
      default:
        return "Dashboard";
    }
  };

  const getPageDescription = () => {
    switch (activeTab) {
      case "dashboard":
        return "Welcome back! Here's what's happening with your admin panel.";
      case "contacts":
        return "Manage and view all contact submissions.";
      case "jobs":
        return "Review and manage job applications.";
      case "invoices":
        return "Create, manage, and track invoices and payments.";
      case "blogs":
        return "Create, edit, and manage your blog content.";
      case "profile":
        return "Manage your account settings and preferences.";
      default:
        return "Welcome back! Here's what's happening with your admin panel.";
    }
  };

  const renderContent = () => {
    if (activeTab === "profile") {
      return (
        <ProfilePage
          user={user}
          linkedinUrl={linkedinUrl}
          onLinkedinUrlChange={setLinkedinUrl}
        />
      );
    }

    if (activeTab === "blogs" && showBlogForm) {
      return (
        <ImprovedBlogForm
          onSubmit={handleBlogSubmit}
          onCancel={() => setShowBlogForm(false)}
        />
      );
    }

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
              {getPageTitle()}
            </h1>
            <p className="text-muted-foreground">
              {getPageDescription()}
            </p>
          </div>
          <div className="flex items-center gap-2">
            {activeTab === "blogs" && (
              <Button onClick={() => setShowBlogForm(true)}>
                <Plus className="w-4 h-4 mr-2" />
                New Blog Post
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {activeTab === "dashboard" && (
          <div className="space-y-6">
            <StatsDashboard
              contacts={contacts}
              applications={applications}
              blogs={blogs}
            />
            <div className="grid gap-6 md:grid-cols-2">
              <RecentActivity
                contacts={contacts.slice(0, 5)}
                applications={applications.slice(0, 5)}
                blogs={blogs.slice(0, 5)}
              />
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={() => setShowBlogForm(true)}
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    Create New Blog Post
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full justify-start"
                    onClick={handleRefresh}
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Refresh All Data
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {activeTab === "contacts" && (
          <EnhancedTable
            data={contacts}
            type="contacts"
            onView={() => {}}
            onDelete={() => {}}
          />
        )}

        {activeTab === "jobs" && (
          <EnhancedTable
            data={applications}
            type="applications"
            onView={() => {}}
            onDelete={() => {}}
          />
        )}

        {activeTab === "invoices" && (
          <InvoiceManagement />
        )}

        {activeTab === "blogs" && !showBlogForm && (
          <EnhancedTable
            data={blogs}
            type="blogs"
            onView={() => {}}
            onDelete={() => {}}
          />
        )}
      </div>
    );
  };

  return (
    <SidebarProvider>
      <AppSidebar
        user={user}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        onLogout={handleLogout}
      />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="#">
                  Apromax Admin
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>{getPageTitle()}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </header>
        <div className="flex flex-1 flex-col gap-4 p-4">
          {renderContent()}
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}

export default function AdminPanel() {
  return (
    <ProtectedRoute>
      <AdminPanelContent />
    </ProtectedRoute>
  );
}

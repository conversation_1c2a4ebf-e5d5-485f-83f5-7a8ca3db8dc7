"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Eye,
  Download,
  Loader2,
  LogOut,
  Trash2,
  Refresh<PERSON><PERSON>,
} from "lucide-react";
import { cn, slugify } from "@/lib/utils";
import { toast } from "sonner";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import BlogEditor from "./components/blog-editor";
import { ImageUpload } from "./components/image-upload";
import { AdminHeader } from "./components/admin-header";
import { StatsDashboard, RecentActivity } from "./components/stats-dashboard";
import { EnhancedTable } from "./components/enhanced-table";
import { useAuth } from "@/hooks/useAuth";
import { DatabaseService } from "@/lib/database";
import { StorageService } from "@/lib/storage";
import { Contact, Application, Blog } from "@/lib/supabase";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";

export default function AdminPanel() {
  const router = useRouter();
  const { user, session, loading: authLoading, signOut } = useAuth();
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [applications, setApplications] = useState<Application[]>([]);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [deleteItem, setDeleteItem] = useState<any>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [selectedBlog, setSelectedBlog] = useState<Blog | null>(null);
  const [isEditingBlog, setIsEditingBlog] = useState(false);
  const [blogFormData, setBlogFormData] = useState({
    title: "",
    content: "",
    excerpt: "",
    published: false,
    slug: "",
    author_id: "",
    featured_image: "",
  });
  const [linkedinUrl, setLinkedinUrl] = useState("");
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [currentBlog, setCurrentBlog] = useState<Blog | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize blog form with user ID when user is available
  useEffect(() => {
    if (user) {
      setBlogFormData((prev) => ({
        ...prev,
        author_id: user.id,
      }));
    }
  }, [user]);

  // Fetch data when user is authenticated
  useEffect(() => {
    if (user) {
      fetchData();
      fetchBlogs();
    }
  }, [user]);

  const handleLogout = async () => {
    try {
      await signOut();
      setContacts([]);
      setApplications([]);
      setBlogs([]);
      toast.success("Logged out successfully");
      router.push('/auth/signin');
    } catch (error: any) {
      console.error("Logout error:", error);
      toast.error("Error logging out");
    }
  };

  const fetchData = async () => {
    if (!user) return;

    setIsRefreshing(true);
    try {
      const [contactsData, applicationsData] = await Promise.all([
        DatabaseService.getContacts(),
        DatabaseService.getApplications(),
      ]);

      setContacts(contactsData);
      setApplications(applicationsData);
    } catch (error: any) {
      console.error("Error fetching data:", error);
      toast.error("Error fetching data");
    } finally {
      setIsRefreshing(false);
    }
  };

  const fetchBlogs = async () => {
    if (!user) return;

    try {
      const blogsData = await DatabaseService.getBlogs();
      setBlogs(blogsData);
    } catch (error: any) {
      console.error("Error fetching blogs:", error);
      toast.error("Error fetching blogs");
    }
  };

  const handleDelete = async () => {
    if (!deleteItem) return;

    try {
      switch (deleteItem.type) {
        case "contact":
          await DatabaseService.deleteContact(deleteItem.id);
          break;
        case "application":
          await DatabaseService.deleteApplication(deleteItem.id);
          // Also delete associated resume file if it exists
          if (deleteItem.resume_file_id) {
            try {
              await StorageService.deleteResume(deleteItem.resume_file_id);
            } catch (error) {
              console.warn("Could not delete resume file:", error);
            }
          }
          break;
        case "blog":
          await DatabaseService.deleteBlog(deleteItem.id);
          // Also delete associated featured image if it exists
          if (deleteItem.featured_image) {
            try {
              await StorageService.deleteBlogImage(deleteItem.featured_image);
            } catch (error) {
              console.warn("Could not delete blog image:", error);
            }
          }
          break;
        default:
          return;
      }

      setDeleteItem(null);
      setIsDeleteDialogOpen(false);
      await fetchData();
      await fetchBlogs();
      toast.success("Record deleted successfully");
    } catch (error: any) {
      console.error("Error deleting record:", error);
      toast.error("Error deleting record");
    }
  };

  const generateSlug = (title) => {
    return slugify(title);
  };

  const handleBlogSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    try {
      const blogData = {
        title: blogFormData.title,
        content: blogFormData.content,
        excerpt: blogFormData.excerpt,
        published: blogFormData.published,
        slug: blogFormData.slug || generateSlug(blogFormData.title),
        author_id: user.id,
        featured_image: blogFormData.featured_image || undefined,
      };

      if (currentBlog) {
        await DatabaseService.updateBlog(currentBlog.id, blogData);
        toast.success("Blog updated successfully");
      } else {
        await DatabaseService.createBlog(blogData);
        toast.success("Blog created successfully");
      }

      setIsEditingBlog(false);
      setCurrentBlog(null);
      setBlogFormData({
        title: "",
        content: "",
        excerpt: "",
        published: false,
        slug: "",
        author_id: user.id,
        featured_image: "",
      });
      await fetchBlogs();
    } catch (error: any) {
      console.error("Error saving blog:", error);
      toast.error("Error saving blog");
    }
  };

  const editBlog = (blog: Blog) => {
    setCurrentBlog(blog);
    setBlogFormData({
      title: blog.title,
      content: blog.content,
      excerpt: blog.excerpt,
      published: blog.published,
      slug: blog.slug,
      author_id: blog.author_id || (user?.id || ""),
      featured_image: blog.featured_image || "",
    });
    setIsEditingBlog(true);
  };

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setIsLoading(true);

    try {
      const existingProfile = await DatabaseService.getAdminProfile(user.id);

      if (existingProfile) {
        await DatabaseService.updateAdminProfile(user.id, {
          linkedin_url: linkedinUrl
        });
      } else {
        await DatabaseService.createAdminProfile({
          user_id: user.id,
          linkedin_url: linkedinUrl
        });
      }

      setIsEditingProfile(false);
      toast.success("Profile updated successfully");
    } catch (error: any) {
      console.error("Error updating profile:", error);
      toast.error("Error updating profile");
    } finally {
      setIsLoading(false);
    }
  };

  const confirmDelete = (item: any, type: string) => {
    setDeleteItem({ ...item, type });
    setIsDeleteDialogOpen(true);
  };

  const viewDetails = (item: any) => {
    setSelectedItem(item);
    setIsDialogOpen(true);
  };

  const handleImageUpload = async (file: File) => {
    try {
      const upload = await StorageService.uploadBlogImage(file);

      setBlogFormData((prev) => ({
        ...prev,
        featured_image: upload.path,
      }));

      toast.success("Image uploaded successfully");
    } catch (error: any) {
      console.error("Error uploading image:", error);
      toast.error("Error uploading image");
    }
  };

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950">
        <div className="text-center space-y-4">
          <Loader2 className="w-12 h-12 animate-spin text-blue-600 mx-auto" />
          <p className="text-lg text-muted-foreground">
            Loading admin panel...
          </p>
        </div>
      </div>
    );
  }

  if (!user) {
    // Redirect to sign-in page
    router.push('/auth/signin')
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <div className="text-center space-y-4">
          <Loader2 className="w-12 h-12 animate-spin text-blue-600 mx-auto" />
          <p className="text-lg text-muted-foreground">
            Redirecting to sign in...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <AdminHeader
        user={user}
        onLogout={handleLogout}
        onRefresh={fetchData}
        isRefreshing={isRefreshing}
      />

      <div className="container mx-auto px-4 py-8">
        <Tabs defaultValue="dashboard" className="w-full">
          <div className="flex items-center justify-between mb-8">
            <TabsList className="grid w-full max-w-md grid-cols-4 bg-muted/50">
              <TabsTrigger value="dashboard" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
                Dashboard
              </TabsTrigger>
              <TabsTrigger value="contacts" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
                Contacts
              </TabsTrigger>
              <TabsTrigger value="applications" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
                Jobs
              </TabsTrigger>
              <TabsTrigger value="blogs" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
                Blogs
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="dashboard" className="space-y-6">
            <div>
              <h2 className="text-3xl font-bold tracking-tight mb-2">Dashboard</h2>
              <p className="text-muted-foreground">
                Welcome back! Here's what's happening with your admin panel.
              </p>
            </div>

            <StatsDashboard
              contacts={contacts}
              applications={applications}
              blogs={blogs}
            />

            <div className="grid gap-6 md:grid-cols-2">
              <RecentActivity
                contacts={contacts}
                applications={applications}
                blogs={blogs}
              />

              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    onClick={() => {
                      setIsEditingBlog(true);
                      setCurrentBlog(null);
                      setBlogFormData({
                        title: "",
                        content: "",
                        excerpt: "",
                        published: false,
                        slug: "",
                        author_id: user?.id || "",
                        featured_image: "",
                      });
                    }}
                    className="w-full justify-start"
                    variant="outline"
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    Create New Blog Post
                  </Button>
                  <Button
                    onClick={fetchData}
                    className="w-full justify-start"
                    variant="outline"
                    disabled={isRefreshing}
                  >
                    <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                    Refresh All Data
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="contacts" className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold tracking-tight">Contacts</h2>
                <p className="text-muted-foreground">
                  Manage customer inquiries and contact requests.
                </p>
              </div>
              <Badge variant="outline" className="text-lg px-3 py-1">
                {contacts.length} Total
              </Badge>
            </div>

            <Card className="border-0 shadow-sm">
              <CardContent className="p-6">
                <EnhancedTable
                  data={contacts}
                  type="contacts"
                  onView={viewDetails}
                  onDelete={(contact) => confirmDelete(contact, "contact")}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="applications" className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold tracking-tight">Job Applications</h2>
                <p className="text-muted-foreground">
                  Review and manage job applications and resumes.
                </p>
              </div>
              <Badge variant="outline" className="text-lg px-3 py-1">
                {applications.length} Total
              </Badge>
            </div>

            <Card className="border-0 shadow-sm">
              <CardContent className="p-6">
                <EnhancedTable
                  data={applications}
                  type="applications"
                  onView={viewDetails}
                  onDelete={(application) => confirmDelete(application, "application")}
                  onDownload={async (application) => {
                    if (application.resume_file_id) {
                      try {
                        const url = await StorageService.getResumeUrl(application.resume_file_id);
                        window.open(url, "_blank");
                      } catch (error) {
                        toast.error("Error downloading resume");
                      }
                    }
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="blogs" className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold tracking-tight">Blog Management</h2>
                <p className="text-muted-foreground">
                  Create, edit, and manage your blog content.
                </p>
              </div>
              <div className="flex items-center gap-3">
                <Badge variant="outline" className="text-lg px-3 py-1">
                  {blogs.length} Total
                </Badge>
                <Button
                  onClick={() => {
                    setIsEditingBlog(true);
                    setCurrentBlog(null);
                    setBlogFormData({
                      title: "",
                      content: "",
                      excerpt: "",
                      published: false,
                      slug: "",
                      author_id: user?.id || "",
                      featured_image: "",
                    });
                  }}
                  className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  New Blog Post
                </Button>
              </div>
            </div>

            {isEditingBlog ? (
              <Card className="border-0 shadow-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    {currentBlog ? "Edit Blog Post" : "Create New Blog Post"}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleBlogSubmit} className="space-y-6">
                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label htmlFor="title">Title</Label>
                        <Input
                          id="title"
                          value={blogFormData.title}
                          onChange={(e) =>
                            setBlogFormData((prev) => ({
                              ...prev,
                              title: e.target.value,
                            }))
                          }
                          required
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="slug">Slug</Label>
                        <Input
                          id="slug"
                          value={blogFormData.slug}
                          onChange={(e) =>
                            setBlogFormData((prev) => ({
                              ...prev,
                              slug: e.target.value,
                            }))
                          }
                          placeholder="Auto-generated from title"
                          className="mt-1"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="excerpt">Excerpt</Label>
                      <Textarea
                        id="excerpt"
                        value={blogFormData.excerpt}
                        onChange={(e) =>
                          setBlogFormData((prev) => ({
                            ...prev,
                            excerpt: e.target.value,
                          }))
                        }
                        required
                        className="mt-1"
                        rows={3}
                      />
                    </div>

                    <div>
                      <Label>Content</Label>
                      <div className="mt-1">
                        <BlogEditor
                          content={blogFormData.content}
                          onChange={(content) =>
                            setBlogFormData((prev) => ({
                              ...prev,
                              content,
                            }))
                          }
                        />
                      </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div>
                        <Label htmlFor="featuredImage">Featured Image</Label>
                        <div className="mt-1">
                          <ImageUpload
                            onUpload={handleImageUpload}
                            currentImageId={blogFormData.featured_image}
                            bucketId="blog-images"
                          />
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="published"
                            checked={blogFormData.published}
                            onCheckedChange={(checked) =>
                              setBlogFormData((prev) => ({
                                ...prev,
                                published: checked,
                              }))
                            }
                          />
                          <Label htmlFor="published">Publish immediately</Label>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div className="flex justify-end space-x-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setIsEditingBlog(false);
                          setCurrentBlog(null);
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
                      >
                        {currentBlog ? "Update" : "Create"} Blog Post
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            ) : (
              <Card className="border-0 shadow-sm">
                <CardContent className="p-6">
                  <EnhancedTable
                    data={blogs}
                    type="blogs"
                    onView={viewDetails}
                    onEdit={editBlog}
                    onDelete={(blog) => confirmDelete(blog, "blog")}
                  />
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        {/* View Details Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
                Details
              </DialogTitle>
            </DialogHeader>
            {selectedItem && (
              <div className="grid gap-4 py-4">
                {Object.entries(selectedItem).map(([key, value]) => {
                  if (key === "id" || key === "created_at" || key === "updated_at") return null;
                  return (
                    <div key={key} className="grid grid-cols-3 items-start">
                      <span className="font-medium capitalize text-muted-foreground">
                        {key.replace(/_/g, " ").replace(/([A-Z])/g, " $1").trim()}
                      </span>
                      <span className="col-span-2">{value?.toString()}</span>
                    </div>
                  );
                })}
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <AlertDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the
                record
                {deleteItem?.type === "application" &&
                  " and associated resume file"}
                .
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                className="bg-gradient-to-r from-rose-600 to-pink-600 text-white hover:from-rose-700 hover:to-pink-700"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
}

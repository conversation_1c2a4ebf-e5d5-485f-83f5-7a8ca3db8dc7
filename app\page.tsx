"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Eye,
  Download,
  Loader2,
  LogOut,
  Trash2,
  Refresh<PERSON><PERSON>,
} from "lucide-react";
import { cn, slugify } from "@/lib/utils";
import { toast } from "sonner";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import BlogEditor from "./components/blog-editor";
import { ImageUpload } from "./components/image-upload";
import { useAuth } from "@/hooks/useAuth";
import { DatabaseService } from "@/lib/database";
import { StorageService } from "@/lib/storage";
import { Contact, Application, Blog } from "@/lib/supabase";

export default function AdminPanel() {
  const router = useRouter();
  const { user, session, loading: authLoading, signIn, signOut } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [applications, setApplications] = useState<Application[]>([]);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [deleteItem, setDeleteItem] = useState<any>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [selectedBlog, setSelectedBlog] = useState<Blog | null>(null);
  const [isEditingBlog, setIsEditingBlog] = useState(false);
  const [blogFormData, setBlogFormData] = useState({
    title: "",
    content: "",
    excerpt: "",
    published: false,
    slug: "",
    author_id: "",
    featured_image: "",
  });
  const [linkedinUrl, setLinkedinUrl] = useState("");
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [currentBlog, setCurrentBlog] = useState<Blog | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize blog form with user ID when user is available
  useEffect(() => {
    if (user) {
      setBlogFormData((prev) => ({
        ...prev,
        author_id: user.id,
      }));
    }
  }, [user]);

  // Fetch data when user is authenticated
  useEffect(() => {
    if (user) {
      fetchData();
      fetchBlogs();
    }
  }, [user]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await signIn(email, password);
      toast.success("Login successful");
    } catch (error: any) {
      console.error("Login error:", error);
      let errorMessage = "Authentication failed. Please check your credentials.";

      if (error.message?.includes("Invalid login credentials")) {
        errorMessage = "Invalid email or password. Please try again.";
      } else if (error.message?.includes("Email not confirmed")) {
        errorMessage = "Please confirm your email address before signing in.";
      }

      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await signOut();
      setContacts([]);
      setApplications([]);
      setBlogs([]);
      toast.success("Logged out successfully");
    } catch (error: any) {
      console.error("Logout error:", error);
      toast.error("Error logging out");
    }
  };

  const fetchData = async () => {
    if (!user) return;

    setIsRefreshing(true);
    try {
      const [contactsData, applicationsData] = await Promise.all([
        DatabaseService.getContacts(),
        DatabaseService.getApplications(),
      ]);

      setContacts(contactsData);
      setApplications(applicationsData);
    } catch (error: any) {
      console.error("Error fetching data:", error);
      toast.error("Error fetching data");
    } finally {
      setIsRefreshing(false);
    }
  };

  const fetchBlogs = async () => {
    if (!user) return;

    try {
      const blogsData = await DatabaseService.getBlogs();
      setBlogs(blogsData);
    } catch (error: any) {
      console.error("Error fetching blogs:", error);
      toast.error("Error fetching blogs");
    }
  };

  const handleDelete = async () => {
    if (!deleteItem) return;

    try {
      switch (deleteItem.type) {
        case "contact":
          await DatabaseService.deleteContact(deleteItem.id);
          break;
        case "application":
          await DatabaseService.deleteApplication(deleteItem.id);
          // Also delete associated resume file if it exists
          if (deleteItem.resume_file_id) {
            try {
              await StorageService.deleteResume(deleteItem.resume_file_id);
            } catch (error) {
              console.warn("Could not delete resume file:", error);
            }
          }
          break;
        case "blog":
          await DatabaseService.deleteBlog(deleteItem.id);
          // Also delete associated featured image if it exists
          if (deleteItem.featured_image) {
            try {
              await StorageService.deleteBlogImage(deleteItem.featured_image);
            } catch (error) {
              console.warn("Could not delete blog image:", error);
            }
          }
          break;
        default:
          return;
      }

      setDeleteItem(null);
      setIsDeleteDialogOpen(false);
      await fetchData();
      await fetchBlogs();
      toast.success("Record deleted successfully");
    } catch (error: any) {
      console.error("Error deleting record:", error);
      toast.error("Error deleting record");
    }
  };

  const generateSlug = (title) => {
    return slugify(title);
  };

  const handleBlogSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    try {
      const blogData = {
        title: blogFormData.title,
        content: blogFormData.content,
        excerpt: blogFormData.excerpt,
        published: blogFormData.published,
        slug: blogFormData.slug || generateSlug(blogFormData.title),
        author_id: user.id,
        featured_image: blogFormData.featured_image || undefined,
      };

      if (currentBlog) {
        await DatabaseService.updateBlog(currentBlog.id, blogData);
        toast.success("Blog updated successfully");
      } else {
        await DatabaseService.createBlog(blogData);
        toast.success("Blog created successfully");
      }

      setIsEditingBlog(false);
      setCurrentBlog(null);
      setBlogFormData({
        title: "",
        content: "",
        excerpt: "",
        published: false,
        slug: "",
        author_id: user.id,
        featured_image: "",
      });
      await fetchBlogs();
    } catch (error: any) {
      console.error("Error saving blog:", error);
      toast.error("Error saving blog");
    }
  };

  const editBlog = (blog: Blog) => {
    setCurrentBlog(blog);
    setBlogFormData({
      title: blog.title,
      content: blog.content,
      excerpt: blog.excerpt,
      published: blog.published,
      slug: blog.slug,
      author_id: blog.author_id || (user?.id || ""),
      featured_image: blog.featured_image || "",
    });
    setIsEditingBlog(true);
  };

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setIsLoading(true);

    try {
      const existingProfile = await DatabaseService.getAdminProfile(user.id);

      if (existingProfile) {
        await DatabaseService.updateAdminProfile(user.id, {
          linkedin_url: linkedinUrl
        });
      } else {
        await DatabaseService.createAdminProfile({
          user_id: user.id,
          linkedin_url: linkedinUrl
        });
      }

      setIsEditingProfile(false);
      toast.success("Profile updated successfully");
    } catch (error: any) {
      console.error("Error updating profile:", error);
      toast.error("Error updating profile");
    } finally {
      setIsLoading(false);
    }
  };

  const confirmDelete = (item: any, type: string) => {
    setDeleteItem({ ...item, type });
    setIsDeleteDialogOpen(true);
  };

  const viewDetails = (item: any) => {
    setSelectedItem(item);
    setIsDialogOpen(true);
  };

  const handleImageUpload = async (file: File) => {
    try {
      const upload = await StorageService.uploadBlogImage(file);

      setBlogFormData((prev) => ({
        ...prev,
        featured_image: upload.path,
      }));

      toast.success("Image uploaded successfully");
    } catch (error: any) {
      console.error("Error uploading image:", error);
      toast.error("Error uploading image");
    }
  };

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950">
        <div className="text-center space-y-4">
          <Loader2 className="w-12 h-12 animate-spin text-blue-600 mx-auto" />
          <p className="text-lg text-muted-foreground">
            Loading admin panel...
          </p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-950">
        <div className="container mx-auto px-4 py-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="max-w-md mx-auto"
          >
            <Card className="border-t-4 border-t-blue-500">
              <CardContent className="pt-6">
                <h1 className="text-3xl font-bold text-center mb-2 bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
                  Admin Login
                </h1>
                <p className="text-center text-muted-foreground mb-6">
                  Please sign in to access the admin panel
                </p>
                <form onSubmit={handleLogin} className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Email</label>
                    <Input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      className="border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Password</label>
                    <Input
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      className="border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                  <Button
                    type="submit"
                    className={cn(
                      "w-full h-11",
                      "bg-gradient-to-r from-blue-600 to-cyan-600",
                      "hover:from-blue-700 hover:to-cyan-700",
                      "text-white font-medium",
                      "transition-all duration-200",
                      "rounded-lg"
                    )}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Signing in...
                      </>
                    ) : (
                      "Sign In"
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto py-10">
        <Tabs defaultValue="contacts" className="w-full">
          <div className="flex items-center justify-between mb-4">
            <TabsList className="w-[400px] grid grid-cols-3">
              <TabsTrigger
                value="contacts"
                className={cn(
                  "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                )}
              >
                Contacts
              </TabsTrigger>
              <TabsTrigger
                value="applications"
                className={cn(
                  "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                )}
              >
                Applications
              </TabsTrigger>
              <TabsTrigger
                value="blogs"
                className={cn(
                  "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                )}
              >
                Blogs
              </TabsTrigger>
            </TabsList>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={fetchData}
                disabled={isRefreshing}
                className={cn(
                  "text-gray-600 hover:text-gray-900",
                  "dark:text-gray-400 dark:hover:text-gray-100"
                )}
              >
                {isRefreshing ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Refreshing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Refresh
                  </>
                )}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleLogout}
                className={cn(
                  "text-red-600 hover:text-red-700",
                  "dark:text-red-500 dark:hover:text-red-400"
                )}
              >
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>

          <TabsContent value="contacts">
            <Card>
              <CardContent className="p-6">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50 dark:bg-gray-800">
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Company</TableHead>
                      <TableHead>Service</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {contacts.map((contact) => (
                      <TableRow
                        key={contact.$id}
                        className="hover:bg-gray-50 dark:hover:bg-gray-800/50"
                      >
                        <TableCell className="font-medium">
                          {contact.first_name} {contact.last_name}
                        </TableCell>
                        <TableCell>{contact.email}</TableCell>
                        <TableCell>{contact.company_name}</TableCell>
                        <TableCell>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {contact.service}
                          </span>
                        </TableCell>
                        <TableCell className="text-right space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => viewDetails(contact)}
                            className="hover:text-blue-600"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => confirmDelete(contact, "contact")}
                            className="hover:text-rose-600"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="applications">
            <Card>
              <CardContent className="p-6">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50 dark:bg-gray-800">
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Position</TableHead>
                      <TableHead>Experience</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {applications.map((application) => (
                      <TableRow
                        key={application.$id}
                        className="hover:bg-gray-50 dark:hover:bg-gray-800/50"
                      >
                        <TableCell className="font-medium">
                          {application.name}
                        </TableCell>
                        <TableCell>{application.email}</TableCell>
                        <TableCell>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800">
                            {application.position}
                          </span>
                        </TableCell>
                        <TableCell>{application.experience} years</TableCell>
                        <TableCell className="text-right space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => viewDetails(application)}
                            className="hover:text-blue-600"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={async () => {
                              if (application.resume_file_id) {
                                try {
                                  const url = await StorageService.getResumeUrl(application.resume_file_id);
                                  window.open(url, "_blank");
                                } catch (error) {
                                  toast.error("Error downloading resume");
                                }
                              }
                            }}
                            className="hover:text-emerald-600"
                            disabled={!application.resume_file_id}
                          >
                            <Download className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              confirmDelete(application, "application")
                            }
                            className="hover:text-rose-600"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="blogs">
            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between mb-6">
                  <h2 className="text-2xl font-bold">Blog Posts</h2>
                  <Button
                    onClick={() => {
                      setIsEditingBlog(true);
                      setCurrentBlog(null);
                      setBlogFormData({
                        title: "",
                        content: "",
                        excerpt: "",
                        published: false,
                        slug: "",
                        author_id: "",
                        featured_image: "",
                      });
                    }}
                  >
                    New Blog Post
                  </Button>
                </div>

                {isEditingBlog ? (
                  <form onSubmit={handleBlogSubmit} className="space-y-4">
                    <div>
                      <Label htmlFor="title">Title</Label>
                      <Input
                        id="title"
                        value={blogFormData.title}
                        onChange={(e) =>
                          setBlogFormData((prev) => ({
                            ...prev,
                            title: e.target.value,
                          }))
                        }
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="excerpt">Excerpt</Label>
                      <Textarea
                        id="excerpt"
                        value={blogFormData.excerpt}
                        onChange={(e) =>
                          setBlogFormData((prev) => ({
                            ...prev,
                            excerpt: e.target.value,
                          }))
                        }
                        required
                      />
                    </div>

                    <div>
                      <Label>Content</Label>
                      <BlogEditor
                        content={blogFormData.content}
                        onChange={(content) =>
                          setBlogFormData((prev) => ({
                            ...prev,
                            content,
                          }))
                        }
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="published"
                        checked={blogFormData.published}
                        onCheckedChange={(checked) =>
                          setBlogFormData((prev) => ({
                            ...prev,
                            published: checked,
                          }))
                        }
                      />
                      <Label htmlFor="published">Published</Label>
                    </div>

                    <div>
                      <Label htmlFor="featuredImage">Featured Image</Label>
                      <ImageUpload
                        onUpload={handleImageUpload}
                        currentImageId={blogFormData.featured_image}
                        bucketId="blog-images"
                      />
                    </div>

                    <div className="flex space-x-2">
                      <Button type="submit">
                        {currentBlog ? "Update" : "Create"} Blog Post
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setIsEditingBlog(false);
                          setCurrentBlog(null);
                        }}
                      >
                        Cancel
                      </Button>
                    </div>
                  </form>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Title</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Created At</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {blogs.map((blog) => (
                        <TableRow key={blog.$id}>
                          <TableCell className="font-medium">
                            {blog.title}
                          </TableCell>
                          <TableCell>
                            <span
                              className={cn(
                                "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                                blog.published
                                  ? "bg-green-100 text-green-800"
                                  : "bg-yellow-100 text-yellow-800"
                              )}
                            >
                              {blog.published ? "Published" : "Draft"}
                            </span>
                          </TableCell>
                          <TableCell>
                            {new Date(blog.created_at).toLocaleDateString()}
                          </TableCell>
                          <TableCell className="text-right space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setCurrentBlog(blog);
                                setBlogFormData({
                                  title: blog.title,
                                  content: blog.content,
                                  excerpt: blog.excerpt,
                                  published: blog.published,
                                  slug: blog.slug,
                                  author_id: blog.author_id || (user?.id || ""),
                                  featured_image: blog.featured_image || "",
                                });
                                setIsEditingBlog(true);
                              }}
                            >
                              Edit
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-600 hover:text-red-700"
                              onClick={() => confirmDelete(blog, "blog")}
                            >
                              Delete
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* View Details Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
                Details
              </DialogTitle>
            </DialogHeader>
            {selectedItem && (
              <div className="grid gap-4 py-4">
                {Object.entries(selectedItem).map(([key, value]) => {
                  if (key === "id" || key === "created_at" || key === "updated_at") return null;
                  return (
                    <div key={key} className="grid grid-cols-3 items-start">
                      <span className="font-medium capitalize text-muted-foreground">
                        {key.replace(/_/g, " ").replace(/([A-Z])/g, " $1").trim()}
                      </span>
                      <span className="col-span-2">{value?.toString()}</span>
                    </div>
                  );
                })}
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <AlertDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the
                record
                {deleteItem?.type === "application" &&
                  " and associated resume file"}
                .
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                className="bg-gradient-to-r from-rose-600 to-pink-600 text-white hover:from-rose-700 hover:to-pink-700"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
}

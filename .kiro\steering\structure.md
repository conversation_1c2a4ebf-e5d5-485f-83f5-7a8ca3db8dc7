# Project Structure

## Directory Organization

```
apromax-admin/
├── app/                    # Next.js App Router
│   ├── components/         # Page-specific components
│   │   ├── blog-editor.tsx
│   │   └── image-upload.tsx
│   ├── globals.css         # Global styles with Tailwind
│   ├── layout.tsx          # Root layout with fonts
│   └── page.tsx            # Main admin panel page
├── components/             # Shared components
│   └── ui/                 # shadcn/ui components
│       ├── button.tsx
│       ├── card.tsx
│       ├── dialog.tsx
│       ├── input.tsx
│       ├── table.tsx
│       └── tabs.tsx
├── lib/                    # Utility functions
│   └── utils.ts            # cn() helper and utilities
├── public/                 # Static assets
└── .kiro/                  # Kiro configuration
    └── steering/           # AI assistant guidance
```

## Architecture Patterns

### Component Organization
- **UI Components**: Reusable components in `components/ui/` (shadcn/ui)
- **Feature Components**: Page-specific components in `app/components/`
- **Shared Components**: Cross-feature components in `components/`

### State Management
- React hooks (`useState`, `useEffect`) for local state
- No global state management library (Redux/Zustand) currently used
- Appwrite client handles backend state

### Styling Conventions
- **Tailwind CSS** utility classes for styling
- **CSS Variables** for theme colors (defined in globals.css)
- **cn()** utility function for conditional classes
- **Dark mode** support via Tailwind's `dark:` prefix

### File Naming
- **kebab-case** for component files (`blog-editor.tsx`)
- **PascalCase** for component names (`BlogEditor`)
- **camelCase** for variables and functions
- **SCREAMING_SNAKE_CASE** for environment variables

### Import Conventions
- Use `@/` path alias for imports from project root
- Group imports: external libraries first, then internal modules
- Destructure imports when possible

### Code Organization
- Single responsibility principle for components
- Custom hooks for reusable logic
- Utility functions in `lib/utils.ts`
- Environment variables prefixed with `NEXT_PUBLIC_` for client-side access